"use client";

import { useState } from "react";
import { useUser } from "@stackframe/stack";
import { Button } from "@/components/ui/button";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { ChevronLeft, Plus } from "lucide-react";
import { Space } from "@/lib/db";
import { useSpacesQuery, useSpaceListCountsQuery } from "@/lib/queries";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

interface SpaceNavigationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentSpaceId?: string | null;
  onSpaceSelect: (space: Space) => void;
  onCreateSpaceClick: () => void;
}

export function SpaceNavigationModal({
  open,
  onOpenChange,
  currentSpaceId,
  onSpaceSelect,
  onCreateSpaceClick,
}: SpaceNavigationModalProps) {
  const user = useUser();
  const [isLoading, setIsLoading] = useState(false);

  // TanStack Query hooks
  const { data: spaces = [], isLoading: spacesLoading } = useSpacesQuery(user?.id || "");
  const { data: spaceListCounts = {} } = useSpaceListCountsQuery(user?.id || "");

  const handleClose = () => {
    onOpenChange(false);
  };

  const handleSpaceClick = (space: Space) => {
    setIsLoading(true);
    onSpaceSelect(space);
    // Close modal after selection
    setTimeout(() => {
      setIsLoading(false);
      onOpenChange(false);
    }, 100);
  };

  const handleCreateSpaceClick = () => {
    onCreateSpaceClick();
    onOpenChange(false);
  };

  return (
    <MobileDialog open={open} onOpenChange={onOpenChange}>
      <MobileDialogContent className="sm:max-w-[425px]" fullHeight>
        <VisuallyHidden asChild>
          <MobileDialogTitle>Select Space</MobileDialogTitle>
        </VisuallyHidden>
        <MobileDialogHeader className="flex items-start justify-start px-4 pt-4 pb-0">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-6 w-6" />
            <span className="sr-only">Back</span>
          </Button>
        </MobileDialogHeader>

        <div className="flex-1 overflow-y-auto px-4 pb-4">
          {spacesLoading ? (
            <div className="flex items-center justify-center py-8">
              <LoadingSpinner size="md" />
            </div>
          ) : (
            <div className="space-y-2">
              {spaces.map((space) => {
                const listCount = spaceListCounts[space.id] || 0;
                const isActive = space.id === currentSpaceId;

                return (
                  <button
                    key={space.id}
                    type="button"
                    onClick={() => handleSpaceClick(space)}
                    disabled={isLoading}
                    className={`w-full flex items-center justify-between p-4 rounded-lg border transition-colors text-left ${
                      isActive
                        ? "bg-primary/10 border-primary/20 text-primary"
                        : "bg-card border-border hover:bg-muted/50"
                    } ${isLoading ? "opacity-50 cursor-not-allowed" : ""}`}
                  >
                    <div className="flex items-center gap-3 min-w-0 flex-1">
                      <div className="text-2xl flex-shrink-0">
                        {space.icon || "📋"}
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="font-medium truncate">
                          {space.name}
                        </div>
                        {space.description && (
                          <div className="text-sm text-muted-foreground truncate">
                            {space.description}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2 flex-shrink-0">
                      <div className="text-sm text-muted-foreground">
                        {listCount} {listCount === 1 ? "list" : "lists"}
                      </div>
                      {isActive && (
                        <div className="w-2 h-2 bg-primary rounded-full" />
                      )}
                    </div>
                  </button>
                );
              })}

              {/* Create New Space Button */}
              <button
                type="button"
                onClick={handleCreateSpaceClick}
                disabled={isLoading}
                className={`w-full flex items-center gap-3 p-4 rounded-lg border border-dashed border-muted-foreground/30 hover:border-muted-foreground/50 hover:bg-muted/30 transition-colors text-left ${
                  isLoading ? "opacity-50 cursor-not-allowed" : ""
                }`}
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-muted">
                  <Plus className="h-4 w-4" />
                </div>
                <div>
                  <div className="font-medium text-muted-foreground">
                    New Space
                  </div>
                  <div className="text-sm text-muted-foreground/70">
                    Create a new space to organize your lists
                  </div>
                </div>
              </button>
            </div>
          )}
        </div>
      </MobileDialogContent>
    </MobileDialog>
  );
}
